<?php

declare(strict_types=1);

namespace App\V2\Domain\VirtualMeeting;

use App\V2\Domain\Shared\Exception\InfrastructureException;

class InvalidVirtualMeetingException extends InfrastructureException
{
    public static function finishAtMustBeAfterStartAt(\DateTimeImmutable $startAt, \DateTimeImmutable $finishAt): self
    {
        return new self(\sprintf(
            'Finish date (%s) must be after start date (%s)',
            $finishAt->format('Y-m-d H:i:s'),
            $startAt->format('Y-m-d H:i:s')
        ));
    }

    public static function urlRequiredForFixedType(): self
    {
        return new self('URL is required for FIXED type virtual meetings');
    }

    public static function invalidUrl(string $url): self
    {
        return new self(\sprintf('Invalid URL format: %s', $url));
    }
}
