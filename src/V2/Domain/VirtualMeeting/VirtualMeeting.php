<?php

declare(strict_types=1);

namespace App\V2\Domain\VirtualMeeting;

use App\V2\Domain\Shared\Entity\LifeCycleEntity;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends LifeCycleEntity<Uuid>
 */
class VirtualMeeting extends LifeCycleEntity
{
    /**
     * @throws InvalidVirtualMeetingException
     */
    public function __construct(
        Uuid $id,
        private readonly VirtualMeetingType $type,
        private readonly \DateTimeImmutable $startAt,
        private readonly \DateTimeImmutable $finishAt,
        private readonly ?string $url = null,
        \DateTimeImmutable $createdAt,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
    ) {
        $this->validateDates($startAt, $finishAt);
        $this->validateUrl($url, $this->type);

        parent::__construct($id, $createdAt, $updatedAt, $deletedAt);
    }

    public function getType(): VirtualMeetingType
    {
        return $this->type;
    }

    public function getStartAt(): \DateTimeImmutable
    {
        return $this->startAt;
    }

    public function getFinishAt(): \DateTimeImmutable
    {
        return $this->finishAt;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    /**
     * @throws InvalidVirtualMeetingException
     */
    private function validateDates(\DateTimeImmutable $startAt, \DateTimeImmutable $finishAt): void
    {
        if ($finishAt <= $startAt) {
            throw InvalidVirtualMeetingException::finishAtMustBeAfterStartAt($startAt, $finishAt);
        }
    }

    /**
     * @throws InvalidVirtualMeetingException
     */
    private function validateUrl(?string $url, VirtualMeetingType $type): void
    {
        if (null === $url && VirtualMeetingType::Fixed === $type) {
            throw InvalidVirtualMeetingException::urlRequiredForFixedType();
        }

        if (null !== $url && !filter_var($url, FILTER_VALIDATE_URL)) {
            throw InvalidVirtualMeetingException::invalidUrl($url);
        }
    }
}
