<?php

declare(strict_types=1);

namespace App\V2\Domain\VirtualMeeting;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\VirtualMeeting\Exception\VirtualMeetingNotFoundException;

interface VirtualMeetingRepository
{
    /**
     * @throws InfrastructureException
     */
    public function put(VirtualMeeting $virtualMeeting): void;

    /**
     * @throws InfrastructureException
     * @throws VirtualMeetingNotFoundException
     */
    public function findOneBy(VirtualMeetingCriteria $criteria): VirtualMeeting;

    /**
     * @throws InfrastructureException
     */
    public function findBy(VirtualMeetingCriteria $criteria): VirtualMeetingCollection;

    /**
     * @throws InfrastructureException
     * @throws VirtualMeetingNotFoundException
     */
    public function delete(VirtualMeeting $virtualMeeting): void;
}
