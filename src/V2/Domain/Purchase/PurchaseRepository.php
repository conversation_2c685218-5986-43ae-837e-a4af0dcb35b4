<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase;

use App\V2\Domain\Purchase\Exception\PurchaseItemNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;

interface PurchaseRepository
{
    /**
     * @throws PurchaseRepositoryException
     */
    public function put(Purchase $purchase): void;

    /**
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     */
    public function findOneBy(PurchaseCriteria $criteria): Purchase;

    /**
     * @throws PurchaseRepositoryException
     */
    public function findBy(PurchaseCriteria $criteria): PurchaseCollection;

    public function countBy(PurchaseCriteria $criteria): int;

    /**
     * @throws PurchaseRepositoryException
     */
    public function delete(Purchase $purchase): void;

    /**
     * @throws PurchaseRepositoryException
     */
    public function putPurchaseItem(PurchaseItem $purchaseItem): void;

    /**
     * @throws PurchaseRepositoryException
     * @throws PurchaseItemNotFoundException
     */
    public function findOnePurchaseItemBy(PurchaseItemCriteria $criteria): ?PurchaseItem;

    /**
     * @throws PurchaseRepositoryException
     */
    public function findPurchaseItemsBy(PurchaseItemCriteria $criteria): PurchaseItemCollection;

    public function countPurchaseItemsBy(PurchaseItemCriteria $criteria): int;

    /**
     * @throws PurchaseRepositoryException
     */
    public function deletePurchaseItem(PurchaseItem $purchaseItem): void;
}
