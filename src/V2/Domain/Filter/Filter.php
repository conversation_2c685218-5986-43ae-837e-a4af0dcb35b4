<?php

declare(strict_types=1);

namespace App\V2\Domain\Filter;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Identifier;

/**
 * @extends EntityWithId<Id>
 */
class Filter extends EntityWithId
{
    public function __construct(
        Identifier $id,
        private readonly Id $filterCategoryId,
        private readonly string $name,
        private readonly string $code,
        private readonly int $sort,
        private readonly ?Id $parentId = null,
    ) {
        parent::__construct($id);
    }

    public function getFilterCategoryId(): Id
    {
        return $this->filterCategoryId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getSort(): int
    {
        return $this->sort;
    }

    public function getParentId(): ?Id
    {
        return $this->parentId;
    }
}
