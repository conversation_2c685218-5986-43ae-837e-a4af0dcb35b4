<?php

declare(strict_types=1);

namespace App\V2\Domain\Filter\FilterCategory;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Identifier;

/**
 * @extends EntityWithId<Id>
 */
class FilterCategory extends EntityWithId
{
    public function __construct(
        Identifier $id,
        private readonly ?Id $parentId,
        private readonly string $name,
        private readonly int $sort = 0,
    ) {
        parent::__construct($id);
    }

    public function getParentId(): ?Id
    {
        return $this->parentId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSort(): int
    {
        return $this->sort;
    }
}
