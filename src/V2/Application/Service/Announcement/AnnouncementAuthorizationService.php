<?php

declare(strict_types=1);

namespace App\V2\Application\Service\Announcement;

use App\Entity\Announcement as LegacyAnnouncement;
use App\Entity\User;
use App\Service\SettingsService;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;

readonly class AnnouncementAuthorizationService implements AnnouncementAuthorizationServiceInterface
{
    public function __construct(
        private AnnouncementManagerRepository $announcementManagerRepository,
        private SettingsService $settingsService,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws ManagerNotAuthorizedException
     */
    public function ensureUserCanManageAnnouncement(User $user, LegacyAnnouncement $announcement): void
    {
        if ($user->isAdmin()) {
            return;
        }

        if ($user->isManager() && $announcement->getCreatedBy()?->getId() === $user->getId()) {
            return;
        }

        // Shared managers can if sharing is enabled and they are actually shared
        if ($user->isManager() && $this->settingsService->get('app.announcement.managers.sharing')) {
            try {
                $this->announcementManagerRepository->findOneBy(
                    AnnouncementManagerCriteria::createEmpty()
                        ->filterByUserId($user->getId())
                        ->filterByAnnouncementId($announcement->getId())
                );

                return;
            } catch (AnnouncementManagerNotFoundException) {
                // Manager is not shared for this announcement.
            }
        }

        throw ManagerNotAuthorizedException::userNotAuthorized(
            announcement: $announcement,
            user: $user->getEmail() ?? 'Unknown user'
        );
    }
}
