<?php

declare(strict_types=1);

namespace App\V2\Application\Service\Announcement;

use App\Entity\Announcement as LegacyAnnouncement;
use App\Entity\User;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;

interface AnnouncementAuthorizationServiceInterface
{
    /**
     * @throws ManagerNotAuthorizedException
     */
    public function ensureUserCanManageAnnouncement(User $user, LegacyAnnouncement $announcement): void;
}
