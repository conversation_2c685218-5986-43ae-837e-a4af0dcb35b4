<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\UpdateVirtualMeeting;
use App\V2\Application\VirtualMeeting\VirtualMeetingFactory;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;

readonly class UpdateVirtualMeetingCommandHandler
{
    public function __construct(
        private VirtualMeetingRepository $virtualMeetingRepository,
    ) {
    }

    /**
     * @throws InvalidVirtualMeetingException
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function handle(UpdateVirtualMeeting $command): void
    {
        $existingVirtualMeeting = $this->virtualMeetingRepository->findOneBy(
            VirtualMeetingCriteria::createEmpty()
                ->filterById($command->getVirtualMeetingId())
        );

        $updatedVirtualMeeting = VirtualMeetingFactory::updateFromDTO(
            existingVirtualMeeting: $existingVirtualMeeting,
            dto: $command->getVirtualMeetingDto(),
        );

        $this->virtualMeetingRepository->put($updatedVirtualMeeting);
    }
}
