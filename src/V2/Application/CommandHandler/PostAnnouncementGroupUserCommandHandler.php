<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\User;
use App\Repository\AnnouncementGroupRepository;
use App\Repository\AnnouncementRepository;
use App\Repository\AnnouncementUserRepository;
use App\V2\Application\Command\PostAnnouncementGroupUserCommand;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationServiceInterface;
use App\V2\Domain\Announcement\Exception\AnnouncementGroupNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Exception\PostAnnouncementGroupUserException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Filter\FilterCollection;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class PostAnnouncementGroupUserCommandHandler
{
    public function __construct(
        private AnnouncementRepository $announcementRepository,
        private AnnouncementGroupRepository $announcementGroupRepository,
        private AnnouncementUserRepository $announcementUserRepository,
        private UserRepository $userRepository,
        private AnnouncementAuthorizationServiceInterface $announcementAuthorizationService,
    ) {
    }

    /**
     * @throws PostAnnouncementGroupUserException
     * @throws AnnouncementNotFoundException
     * @throws AnnouncementGroupNotFoundException
     * @throws ManagerNotAuthorizedException
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function handle(PostAnnouncementGroupUserCommand $command): void
    {
        // 1. Get entities
        $announcement = $this->announcementRepository->findOneBy(['id' => $command->getAnnouncementId()->value()]);
        if (null === $announcement) {
            throw new AnnouncementNotFoundException();
        }

        $group = $this->announcementGroupRepository->findOneBy([
            'id' => $command->getGroupId()->value(),
            'announcement' => $command->getAnnouncementId()->value(),
        ]);
        if (null === $group) {
            throw new AnnouncementGroupNotFoundException();
        }

        $userCriteria = UserCriteria::createById($command->getUserId());

        // If request user is manager (not admin), validate they can manage this user
        if ($command->getRequestUser()->isManager() && !$command->getRequestUser()->isAdmin()) {
            $userCriteria->filterByFilters(
                filters: new FilterCollection($command->getRequestUser()->getFilters()->toArray()),
                addCreatedBy: $command->getRequestUser()->getId(),
            );
        }

        $user = $this->userRepository->findOneBy($userCriteria);

        // 2. Validate permissions
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
            user: $command->getRequestUser(),
            announcement: $announcement,
        );

        // 3. Validate business rules
        $this->ensureUserIsNotInAnnouncement($user, $announcement);
        $this->ensureMaxGroupSizeNotBeenReached($group, $announcement);

        // 4. Create AnnouncementUser
        $this->createAnnouncementUser($announcement, $group, $user);
    }

    /**
     * @throws PostAnnouncementGroupUserException
     */
    private function ensureUserIsNotInAnnouncement(User $user, Announcement $announcement): void
    {
        // Check if user is already in this announcement (any group)
        $existingAnnouncementUser = $this->announcementUserRepository->findOneBy([
            'announcement' => $announcement,
            'user' => $user,
        ]);

        if (null !== $existingAnnouncementUser) {
            throw PostAnnouncementGroupUserException::userAlreadyInAnnouncement();
        }
    }

    /**
     * @throws PostAnnouncementGroupUserException
     */
    private function ensureMaxGroupSizeNotBeenReached(AnnouncementGroup $group, Announcement $announcement): void
    {
        $maxUsers = $announcement->getUsersPerGroup();
        if ($maxUsers <= 0) {
            return; // No limit set
        }

        $currentGroupSize = $this->announcementUserRepository->count([
            'announcementGroup' => $group,
        ]);

        if ($currentGroupSize >= $maxUsers) {
            throw PostAnnouncementGroupUserException::maxGroupSizeReached();
        }
    }

    private function createAnnouncementUser(Announcement $announcement, AnnouncementGroup $group, User $user): void
    {
        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncement($announcement);
        $announcementUser->setAnnouncementGroup($group);
        $announcementUser->setUser($user);
        $announcementUser->setExternal(false);

        $this->announcementUserRepository->persist($announcementUser);
    }
}
