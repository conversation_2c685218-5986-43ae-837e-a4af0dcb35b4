<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Uuid\Uuid;

readonly class UpdateVirtualMeeting implements Command
{
    public function __construct(
        private Uuid $virtualMeetingId,
        private VirtualMeetingDTO $virtualMeetingDto,
    ) {
    }

    public function getVirtualMeetingId(): Uuid
    {
        return $this->virtualMeetingId;
    }

    public function getVirtualMeetingDto(): VirtualMeetingDTO
    {
        return $this->virtualMeetingDto;
    }
}
