<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Service\Announcement;

use App\Entity\Announcement;
use App\Entity\User;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationService;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class AnnouncementAuthorizationServiceTest extends TestCase
{
    private function getService(
        ?AnnouncementManagerRepository $announcementManagerRepository = null,
        ?SettingsService $settingsService = null,
    ): AnnouncementAuthorizationService {
        return new AnnouncementAuthorizationService(
            announcementManagerRepository: $announcementManagerRepository
                ?? $this->createMock(AnnouncementManagerRepository::class),
            settingsService: $settingsService ?? $this->createMock(SettingsService::class)
        );
    }

    public function testAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));
        $service = $this->getService();
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    public function testAsCreator(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_CREATOR]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));

        $service = $this->getService();
        $this->expectExceptionObject(
            ManagerNotAuthorizedException::userNotAuthorized(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    public static function provideAsManager(): \Generator
    {
        $user1 = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_MANAGER]);
        $announcement1 = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1), createdBy: $user1);
        yield 'is the creator' => [
            'user' => $user1,
            'announcement' => $announcement1,
            'sharing' => null,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => null,
        ];

        $announcement2 = AnnouncementMother::create(
            id: 1,
            course: CourseMother::create(id: 1),
            createdBy: UserMother::create(id: 2)
        );
        yield 'not the creator and sharing disabled' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => false,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => ManagerNotAuthorizedException::userNotAuthorized(
                announcement: $announcement2,
                user: '<EMAIL>'
            ),
        ];

        yield 'not the creator and sharing enabled with access' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => AnnouncementManagerMother::create(
                userId: new Id(1),
                announcementId: new Id(2),
            ),
            'exception' => null,
        ];

        yield 'not the creator and sharing enabled without access' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => throw new AnnouncementManagerNotFoundException(),
            'exception' => ManagerNotAuthorizedException::userNotAuthorized(
                announcement: $announcement2,
                user: '<EMAIL>'
            ),
        ];
    }

    #[DataProvider('provideAsManager')]
    public function testAsManager(
        User $user,
        Announcement $announcement,
        ?bool $sharing,
        ?callable $announcementManagerRepositoryFindOneBy,
        ?\Exception $exception = null,
    ): void {
        if (null === $sharing && null === $announcementManagerRepositoryFindOneBy && null === $exception) {
            $this->expectNotToPerformAssertions();
        }

        $settingsService = $this->createMock(SettingsService::class);
        if (null !== $sharing) {
            $settingsService->expects($this->once())
                ->method('get')
                ->with('app.announcement.managers.sharing')
                ->willReturn($sharing);
        }
        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);

        if (null !== $announcementManagerRepositoryFindOneBy) {
            $announcementManagerRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($announcementManagerRepositoryFindOneBy);
        }

        $service = $this->getService(
            announcementManagerRepository: $announcementManagerRepository,
            settingsService: $settingsService,
        );
        if (null !== $exception) {
            $this->expectExceptionObject(
                $exception
            );
        }
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }
}
