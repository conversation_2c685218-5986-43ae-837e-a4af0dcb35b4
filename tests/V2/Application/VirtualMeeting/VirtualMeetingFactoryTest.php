<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\VirtualMeeting;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingDTOMother;
use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingMother;
use App\V2\Application\DTO\VirtualMeetingDTO;
use App\V2\Application\VirtualMeeting\VirtualMeetingFactory;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\VirtualMeeting\InvalidVirtualMeetingException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class VirtualMeetingFactoryTest extends TestCase
{
    /**
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     * @throws \DateMalformedStringException
     */
    public function testCreateFromDTO()
    {
        $virtualMeetingDTO = VirtualMeetingDTOMother::create(
            type: VirtualMeetingType::Fixed,
            url: 'https://example.com/meeting',
        );
        $id = UuidMother::create();

        $virtualMeeting = VirtualMeetingFactory::createFromDTO($id, $virtualMeetingDTO);

        $this->assertInstanceOf(VirtualMeeting::class, $virtualMeeting);

        $this->assertEquals($id, $virtualMeeting->getId());
        $this->assertEquals($virtualMeetingDTO->getType(), $virtualMeeting->getType());
        $this->assertEquals($virtualMeetingDTO->getStartAt(), $virtualMeeting->getStartAt());
        $this->assertEquals($virtualMeetingDTO->getFinishAt(), $virtualMeeting->getFinishAt());
        $this->assertEquals($virtualMeetingDTO->getUrl(), $virtualMeeting->getUrl());
    }

    /**
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     */
    #[DataProvider('invalidDTOProvider')]
    public function testCreateFromDTOWithInvalidData(
        VirtualMeetingDTO $dto,
        InvalidVirtualMeetingException $expectedException,
    ) {
        $this->expectExceptionObject($expectedException);

        VirtualMeetingFactory::createFromDTO(UuidMother::create(), $dto);
    }

    /**
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     * @throws \DateMalformedStringException
     */
    public function testUpdateFromDTO()
    {
        $originalVirtualMeeting = VirtualMeetingMother::create(
            type: VirtualMeetingType::Fixed,
            url: 'https://example.com/original',
        );

        $updatedDTO = VirtualMeetingDTOMother::create(
            type: VirtualMeetingType::Fixed,
            url: 'https://example.com/updated',
        );

        $updatedVirtualMeeting = VirtualMeetingFactory::updateFromDTO($originalVirtualMeeting, $updatedDTO);

        $this->assertInstanceOf(VirtualMeeting::class, $updatedVirtualMeeting);

        // ID should remain the same
        $this->assertEquals($originalVirtualMeeting->getId(), $updatedVirtualMeeting->getId());

        // CreatedAt should remain the same
        $this->assertEquals($originalVirtualMeeting->getCreatedAt(), $updatedVirtualMeeting->getCreatedAt());

        // UpdatedAt should be set to now (we can't test exact time, but we can check it's not null)
        $this->assertNotNull($updatedVirtualMeeting->getUpdatedAt());

        // Updated values should match DTO
        $this->assertEquals($updatedDTO->getType(), $updatedVirtualMeeting->getType());
        $this->assertEquals($updatedDTO->getStartAt(), $updatedVirtualMeeting->getStartAt());
        $this->assertEquals($updatedDTO->getFinishAt(), $updatedVirtualMeeting->getFinishAt());
        $this->assertEquals($updatedDTO->getUrl(), $updatedVirtualMeeting->getUrl());
    }

    /**
     * @throws InvalidVirtualMeetingException
     * @throws InvalidUuidException
     */
    #[DataProvider('invalidUpdateDTOProvider')]
    public function testUpdateFromDTOWithInvalidData(
        VirtualMeetingDTO $dto,
        InvalidVirtualMeetingException $expectedException,
    ) {
        $originalVirtualMeeting = VirtualMeetingMother::create(
            type: VirtualMeetingType::Fixed,
            url: 'https://example.com/original',
        );

        $this->expectExceptionObject($expectedException);

        VirtualMeetingFactory::updateFromDTO($originalVirtualMeeting, $dto);
    }

    public static function invalidUpdateDTOProvider(): \Generator
    {
        yield 'invalid dates' => [
            VirtualMeetingDTOMother::create(
                startAt: new \DateTimeImmutable('2023-10-01 10:00:00'),
                finishAt: new \DateTimeImmutable('2023-10-01 09:00:00') // Invalid finish date
            ),
            InvalidVirtualMeetingException::finishAtMustBeAfterStartAt(
                new \DateTimeImmutable('2023-10-01 10:00:00'),
                new \DateTimeImmutable('2023-10-01 09:00:00')
            ),
        ];

        yield 'invalid URL for fixed type' => [
            VirtualMeetingDTOMother::create(
                type: VirtualMeetingType::Fixed,
                url: null // URL is required for Fixed type
            ),
            InvalidVirtualMeetingException::urlRequiredForFixedType(),
        ];
    }

    public static function invalidDTOProvider(): \Generator
    {
        yield 'invalid dates' => [
            VirtualMeetingDTOMother::create(
                startAt: new \DateTimeImmutable('2023-10-01 10:00:00'),
                finishAt: new \DateTimeImmutable('2023-10-01 09:00:00') // Invalid finish date
            ),
            InvalidVirtualMeetingException::finishAtMustBeAfterStartAt(
                new \DateTimeImmutable('2023-10-01 10:00:00'),
                new \DateTimeImmutable('2023-10-01 09:00:00')
            ),
        ];

        yield 'invalid URL for type' => [
            VirtualMeetingDTOMother::create(
                type: VirtualMeetingType::Fixed,
                url: null // URL is required for WEBINAR type
            ),
            InvalidVirtualMeetingException::urlRequiredForFixedType(),
        ];
    }
}
